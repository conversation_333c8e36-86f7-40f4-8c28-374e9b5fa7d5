from flask import (
    Flask,
    render_template,
    request,
    jsonify,
    redirect,
    url_for,
    send_file,
    Response,
)
import os
import json
import requests
import PyPDF2
import re
import subprocess
import threading

from datetime import datetime
import xml.etree.ElementTree as ET
from rag_service import RAGService
from database import DatabaseManager

app = Flask(__name__)
app.config["UPLOAD_FOLDER"] = "uploads"
app.config["MAX_CONTENT_LENGTH"] = 16 * 1024 * 1024  # 16MB max file size


# 加载配置
def load_config():
    """加载配置文件"""
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print("警告：config.json文件未找到，使用默认配置")
        return {
            "llm": {
                "provider": "openrouter",
                "base_url": "https://openrouter.ai/api/v1",
                "model": "google/gemini-2.5-flash-lite-preview-06-17",
                "max_tokens": 2048,
                "temperature": 0.7,
            }
        }
    except json.JSONDecodeError:
        print("错误：config.json格式不正确")
        return {}


# 全局配置
CONFIG = load_config()

# 初始化数据库管理器
db_manager = DatabaseManager()

# 初始化RAG服务（延迟初始化）
rag_service = None

def get_rag_service():
    """获取RAG服务实例，支持延迟初始化"""
    global rag_service
    if rag_service is None:
        try:
            rag_service = RAGService(CONFIG)
            print("RAG服务初始化成功")
        except Exception as e:
            print(f"RAG服务初始化失败: {e}")
            print("将使用普通对话模式")
            return None
    return rag_service


def call_llm_api(messages, paper_context=None):
    """调用LLM API进行对话"""
    try:
        llm_config = CONFIG.get("llm", {})

        # 构建系统提示词
        system_prompt = """你是一个专业的学术论文阅读助手。你的任务是帮助用户理解和分析学术论文。

请遵循以下准则：
1. 提供准确、专业的学术分析
2. 用清晰易懂的语言解释复杂概念
3. 结合论文内容回答问题
4. 如果问题超出论文范围，请明确说明
5. 保持客观、中性的学术态度

当前论文信息："""

        if paper_context:
            system_prompt += f"""
标题：{paper_context.get('title', '未知')}
作者：{paper_context.get('authors', '未知')}
摘要：{paper_context.get('abstract', '无摘要')}
来源：{paper_context.get('source', '未知')}
"""

        # 构建API请求
        api_messages = [{"role": "system", "content": system_prompt}]
        api_messages.extend(messages)

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",  # OpenRouter要求
            "X-Title": "DocuMancer",  # OpenRouter要求
        }

        payload = {
            "model": llm_config.get(
                "model", "google/gemini-2.5-flash-lite-preview-06-17"
            ),
            "messages": api_messages,
            "max_tokens": llm_config.get("max_tokens", 2048),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": False,
        }

        print(f"正在调用LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_stream(messages, paper_context=None):
    """调用LLM API进行流式对话"""
    try:
        llm_config = CONFIG.get("llm", {})

        # 构建系统提示词
        system_prompt = """你是一个专业的学术论文阅读助手。你的任务是帮助用户理解和分析学术论文。

请遵循以下准则：
1. 提供准确、专业的学术分析
2. 用清晰易懂的语言解释复杂概念
3. 结合论文内容回答问题
4. 如果问题超出论文范围，请明确说明
5. 保持客观、中性的学术态度

当前论文信息："""

        if paper_context:
            system_prompt += f"""
标题：{paper_context.get('title', '未知')}
作者：{paper_context.get('authors', '未知')}
摘要：{paper_context.get('abstract', '无摘要')}
来源：{paper_context.get('source', '未知')}
"""

        # 构建API请求
        api_messages = [{"role": "system", "content": system_prompt}]
        api_messages.extend(messages)

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",  # OpenRouter要求
            "X-Title": "DocuMancer",  # OpenRouter要求
        }

        payload = {
            "model": llm_config.get(
                "model", "google/gemini-2.5-flash-lite-preview-06-17"
            ),
            "messages": api_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": True,
        }

        print(f"正在调用流式LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True,
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode("utf-8")
                    if line_str.startswith("data: "):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_with_rag(messages, paper_id, paper_context=None):
    """调用LLM API进行RAG增强的对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            return call_llm_api(messages, paper_context)

        # 获取RAG服务
        rag_svc = get_rag_service()
        if not rag_svc:
            print(f"RAG服务不可用，回退到普通对话模式")
            return call_llm_api(messages, paper_context)

        # 确保RAG上下文存在
        if not rag_svc.get_or_create_rag_context(paper_id, paper_context):
            print(f"RAG上下文创建失败，回退到普通对话模式")
            return call_llm_api(messages, paper_context)

        # 搜索相关文档
        relevant_docs = rag_svc.search_relevant_documents(
            paper_id, user_message, k=5
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            return call_llm_api(messages, paper_context)

        # 创建RAG增强的提示词
        rag_prompt = rag_svc.create_rag_prompt(
            user_message, relevant_docs, paper_context
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": False,
        }

        print(f"正在调用RAG增强的LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"RAG API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"RAG API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"RAG LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_stream_with_rag(messages, paper_id, paper_context=None):
    """调用LLM API进行RAG增强的流式对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            yield from call_llm_api_stream(messages, paper_context)
            return

        # 获取RAG服务
        rag_svc = get_rag_service()
        if not rag_svc:
            print(f"RAG服务不可用，回退到普通对话模式")
            yield from call_llm_api_stream(messages, paper_context)
            return

        # 确保RAG上下文存在
        if not rag_svc.get_or_create_rag_context(paper_id, paper_context):
            print(f"RAG上下文创建失败，回退到普通对话模式")
            yield from call_llm_api_stream(messages, paper_context)
            return

        # 搜索相关文档
        relevant_docs = rag_svc.search_relevant_documents(
            paper_id, user_message, k=5
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            yield from call_llm_api_stream(messages, paper_context)
            return

        # 创建RAG增强的提示词
        rag_prompt = rag_svc.create_rag_prompt(
            user_message, relevant_docs, paper_context
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用流式LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": True,
        }

        print(f"正在调用RAG增强的流式LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True,
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode("utf-8")
                    if line_str.startswith("data: "):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"RAG流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"RAG流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_llm_api_with_multi_doc_rag(
    messages, main_paper_id, reference_paper_ids, main_paper, reference_papers
):
    """调用LLM API进行多文档RAG增强的对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            return call_llm_api(messages, main_paper)

        # 获取RAG服务
        rag_svc = get_rag_service()
        if not rag_svc:
            print(f"RAG服务不可用，回退到普通对话模式")
            return call_llm_api(messages, main_paper)

        # 确保所有论文的RAG上下文存在
        if not rag_svc.get_or_create_rag_context(main_paper_id, main_paper):
            print(f"主论文RAG上下文创建失败，回退到普通对话模式")
            return call_llm_api(messages, main_paper)

        # 确保参考论文的RAG上下文存在
        for ref_id, ref_paper in zip(reference_paper_ids, reference_papers):
            if not rag_svc.get_or_create_rag_context(ref_id, ref_paper):
                print(f"参考论文 {ref_id} RAG上下文创建失败")

        # 进行多文档搜索
        relevant_docs = rag_svc.search_multi_doc_relevant_documents(
            main_paper_id,
            reference_paper_ids,
            user_message,
            k=8,  # 多文档模式下获取更多相关内容
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            return call_llm_api(messages, main_paper)

        # 创建多文档RAG增强的提示词
        rag_prompt = rag_svc.create_multi_doc_rag_prompt(
            user_message, relevant_docs, main_paper, reference_papers
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": False,
        }

        print(f"正在调用多文档RAG增强的LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30,
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"多文档RAG API响应格式错误: {result}")
                return "抱歉，AI助手暂时无法回复，请稍后再试。"
        else:
            print(f"多文档RAG API调用失败: {response.status_code}, {response.text}")
            return f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"多文档RAG LLM API调用错误: {e}")
        return "抱歉，AI助手遇到了技术问题，请稍后再试。"


def call_pre_read_api(pdf_content, prompt_content):
    """调用预阅读模型API"""
    try:
        pre_read_config = CONFIG.get("pre_read_model", {})

        # 构建系统提示词
        system_prompt = prompt_content

        # 构建用户消息，包含PDF内容
        user_message = f"请分析以下论文内容：\n\n{pdf_content}"

        # 构建API请求
        api_messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message},
        ]

        headers = {
            "Authorization": f'Bearer {pre_read_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",  # OpenRouter要求
            "X-Title": "DocuMancer",  # OpenRouter要求
        }

        payload = {
            "model": pre_read_config.get("model", "google/gemini-2.5-pro"),
            "messages": api_messages,
            "max_tokens": pre_read_config.get("max_tokens", 4096),
            "temperature": pre_read_config.get("temperature", 0.3),
            "stream": False,
        }

        print(f"正在调用预阅读API: {pre_read_config.get('model')}")

        response = requests.post(
            f"{pre_read_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,  # 预阅读可能需要更长时间
        )

        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                print(f"预阅读API响应格式错误: {result}")
                return "抱歉，预阅读服务暂时无法使用，请稍后再试。"
        else:
            print(f"预阅读API调用失败: {response.status_code}, {response.text}")
            return (
                f"预阅读服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"
            )

    except Exception as e:
        print(f"预阅读API调用错误: {e}")
        return "抱歉，预阅读服务遇到了技术问题，请稍后再试。"


# 确保上传目录存在
os.makedirs(app.config["UPLOAD_FOLDER"], exist_ok=True)
os.makedirs("data", exist_ok=True)
os.makedirs("pre_reads", exist_ok=True)  # 确保预阅读目录存在
os.makedirs("hidden_pre_reads", exist_ok=True)  # 确保隐藏预阅读目录存在
os.makedirs("vector_store", exist_ok=True)  # 确保向量数据库目录存在
os.makedirs("posters", exist_ok=True)  # 确保海报输出目录存在

def load_papers():
    """加载已保存的论文数据"""
    return db_manager.get_all_papers()


def save_papers():
    """保存论文数据 - 已弃用，使用数据库直接操作"""
    pass


def extract_pdf_info(file_path):
    """从PDF文件提取论文信息"""
    title = ""
    authors = ""
    abstract = ""
    full_text = ""

    try:
        with open(file_path, "rb") as file:
            pdf_reader = PyPDF2.PdfReader(file)

            # 提取文本内容
            # 尝试读取前几页以获取标题、作者和摘要
            for page_num in range(min(len(pdf_reader.pages), 5)):  # 读取前5页
                full_text += pdf_reader.pages[page_num].extract_text() + "\n"

            # 1. 尝试从PDF元数据获取信息
            metadata = pdf_reader.metadata
            if metadata:
                title = metadata.get("/Title", "").strip()
                authors = metadata.get("/Author", "").strip()

            # 2. 如果元数据没有标题，尝试从文本提取
            if not title:
                lines = full_text.split("\n")
                # 寻找可能作为标题的行：通常较长，可能全大写或首字母大写
                for line in lines[:15]:  # 检查前15行
                    stripped_line = line.strip()
                    if 20 < len(stripped_line) < 200 and stripped_line.isupper():
                        title = stripped_line
                        break
                    elif 20 < len(stripped_line) < 200 and stripped_line[0].isupper() and stripped_line.count(' ') > 2:
                        # 尝试匹配看起来像标题的行（首字母大写，有多个单词）
                        title = stripped_line
                        break
                
                # 进一步尝试，寻找常见的标题模式
                title_patterns = [
                    r"^\s*Title[:\s]*([^\n]+)",
                    r"^\s*Paper Title[:\s]*([^\n]+)",
                ]
                for pattern in title_patterns:
                    match = re.search(pattern, full_text, re.IGNORECASE | re.MULTILINE)
                    if match:
                        title = match.group(1).strip()
                        break

            # 3. 尝试从文本提取作者
            if not authors:
                # 寻找常见的作者模式，通常在标题下方
                author_patterns = [
                    r"(?i)(?:Author(?:s)?|By)[:\s]*([^\n]+)", # "Author(s): Name"
                    r"[\n]([A-Z][a-zA-Z\s\.\-]+(?:and|,\s*|\s+)+[A-Z][a-zA-Z\s\.\-]+)", # "First Last and Second Last"
                ]
                for pattern in author_patterns:
                    match = re.search(pattern, full_text, re.IGNORECASE)
                    if match:
                        authors = match.group(1).strip()
                        # 进一步清理，移除邮箱、机构等
                        authors = re.sub(r"\S*@\S*", "", authors) # 移除邮箱
                        authors = re.sub(r"\(.*?\)", "", authors) # 移除括号内容
                        authors = re.sub(r"\d+", "", authors) # 移除数字（脚注）
                        authors = authors.replace("\n", ", ").strip()
                        break
            
            # 4. 摘要提取（查找Abstract关键词）
            abstract_patterns = [
                r"(?i)abstract[:\s\n]+(.*?)(?=\n\s*(?:keywords?|introduction|1\.|references?|\n\n[A-Z]))",
                r"(?i)abstract[:\s\n]+(.*?)(?=\n\s*(?:keywords?|introduction|1\s|references?))",
                r"(?i)abstract[:\s]*\n+(.*?)(?=\n\s*(?:keywords?|introduction|1\.|references?))",
                r"(?i)abstract[:\s]+(.*?)(?=\n\n)",
            ]

            for pattern in abstract_patterns:
                abstract_match = re.search(pattern, full_text, re.DOTALL)
                if abstract_match:
                    abstract = abstract_match.group(1).strip()
                    # 清理摘要文本
                    abstract = re.sub(r'\s+', ' ', abstract)  # 合并多个空格
                    abstract = re.sub(r'\n+', ' ', abstract)  # 替换换行符
                    if len(abstract) > 50:  # 确保摘要有足够长度
                        abstract = abstract[:1000]  # 限制长度
                        break

            # 5. 如果标题仍然为空，使用文件名作为备用
            if not title:
                title = os.path.splitext(os.path.basename(file_path))[0]
                # 尝试清理文件名中的日期和随机字符串
                title = re.sub(r"^\d{8}_\d{6}_", "", title) # 移除日期时间前缀
                title = re.sub(r"_\d{10,}", "", title) # 移除长数字后缀
                title = title.replace("_", " ").strip()


            return {
                "title": title or "Unknown Title",
                "authors": authors or "Unknown Author",
                "abstract": abstract or "No abstract available",
                "date": datetime.now().strftime("%Y-%m-%d"),
                "source": "PDF Upload",
                "has_pdf": True,
            }
    except Exception as e:
        print(f"PDF解析错误: {e}")
        return {
            "title": os.path.splitext(os.path.basename(file_path))[0] or "PDF Parse Error",
            "authors": "Unknown",
            "abstract": "Failed to extract information from PDF",
            "date": datetime.now().strftime("%Y-%m-%d"),
            "source": "PDF Upload",
            "has_pdf": False,
        }


def download_arxiv_pdf(arxiv_id):
    """从arXiv下载PDF文件"""
    try:
        # 清理arXiv ID
        clean_id = arxiv_id.replace("https://arxiv.org/abs/", "").replace(
            "http://arxiv.org/abs/", ""
        )

        # arXiv PDF URL
        pdf_url = f"http://arxiv.org/pdf/{clean_id}.pdf"

        # 生成本地文件名
        filename = f"arxiv_{clean_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        file_path = os.path.join(app.config["UPLOAD_FOLDER"], filename)

        # 下载PDF文件
        print(f"正在下载arXiv PDF: {pdf_url}")
        response = requests.get(pdf_url, timeout=30, stream=True)

        if response.status_code == 200:
            # 检查响应内容类型
            content_type = response.headers.get("content-type", "")
            if (
                "application/pdf" in content_type
                or "application/octet-stream" in content_type
            ):
                # 保存文件
                with open(file_path, "wb") as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)

                print(f"arXiv PDF下载成功: {file_path}")
                return file_path
            else:
                print(f"下载的文件不是PDF格式: {content_type}")
                return None
        else:
            print(f"arXiv PDF下载失败: HTTP {response.status_code}")
            return None

    except Exception as e:
        print(f"arXiv PDF下载错误: {e}")
        return None


def fetch_arxiv_paper(arxiv_id):
    """从arXiv获取论文信息并下载PDF"""
    try:
        # 清理arXiv ID
        arxiv_id = arxiv_id.replace("https://arxiv.org/abs/", "").replace(
            "http://arxiv.org/abs/", ""
        )

        # 调用arXiv API获取论文信息
        url = f"http://export.arxiv.org/api/query?id_list={arxiv_id}"
        response = requests.get(url, timeout=10)

        if response.status_code == 200:
            # 解析XML响应
            root = ET.fromstring(response.content)
            entry = root.find("{http://www.w3.org/2005/Atom}entry")

            if entry is not None:
                title = entry.find("{http://www.w3.org/2005/Atom}title").text.strip()

                # 获取作者
                authors = []
                for author in entry.findall("{http://www.w3.org/2005/Atom}author"):
                    name = author.find("{http://www.w3.org/2005/Atom}name").text
                    authors.append(name)

                # 获取摘要
                abstract = entry.find(
                    "{http://www.w3.org/2005/Atom}summary"
                ).text.strip()

                # 获取发布日期
                published = entry.find("{http://www.w3.org/2005/Atom}published").text
                date = published.split("T")[0]  # 提取日期部分

                # 下载PDF文件
                pdf_path = download_arxiv_pdf(arxiv_id)

                paper_info = {
                    "title": title,
                    "authors": ", ".join(authors),
                    "abstract": abstract,
                    "date": date,
                    "source": f"arXiv:{arxiv_id}",
                    "arxiv_id": arxiv_id,
                }

                # 如果PDF下载成功，添加文件路径
                if pdf_path:
                    paper_info["file_path"] = pdf_path
                    paper_info["has_pdf"] = True
                else:
                    paper_info["has_pdf"] = False
                    print(f"警告：arXiv论文 {arxiv_id} 的PDF下载失败，但论文信息已获取")

                return paper_info

    except Exception as e:
        print(f"arXiv获取错误: {e}")
        return None





@app.route("/")
def index():
    """论文库主页"""
    papers = load_papers()
    return render_template("index.html", papers=papers)


@app.route("/upload_pdf", methods=["POST"])
def upload_pdf():
    """处理PDF上传"""
    if "pdf_file" not in request.files:
        return jsonify({"error": "No file uploaded"}), 400

    file = request.files["pdf_file"]
    if file.filename == "":
        return jsonify({"error": "No file selected"}), 400

    if file and file.filename.lower().endswith(".pdf"):
        # 保存文件
        filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
        file_path = os.path.join(app.config["UPLOAD_FOLDER"], filename)
        file.save(file_path)

        # 解析PDF信息
        paper_info = extract_pdf_info(file_path)
        paper_info["file_path"] = file_path

        # 检查是否重复
        duplicate_paper = db_manager.check_duplicate_paper(
            paper_info.get("title", ""),
            paper_info.get("authors", "")
        )

        if duplicate_paper:
            # 删除新上传的文件
            try:
                os.remove(file_path)
                print(f"检测到重复论文，已删除新上传的文件: {file_path}")
            except Exception as e:
                print(f"删除重复文件失败: {e}")

            return (
                jsonify(
                    {
                        "success": False,
                        "error": "检测到重复论文",
                        "duplicate_paper": {
                            "id": duplicate_paper["id"],
                            "title": duplicate_paper["title"],
                            "authors": duplicate_paper["authors"],
                            "source": duplicate_paper["source"],
                        },
                        "message": f'论文 "{duplicate_paper["title"]}" 已存在于论文库中',
                    }
                ),
                409,
            )  # 409 Conflict

        # 保存到数据库
        paper_id = db_manager.add_paper(paper_info)
        paper_info["id"] = paper_id

        return jsonify({"success": True, "paper": paper_info})

    return jsonify({"error": "Invalid file type"}), 400


@app.route("/add_arxiv", methods=["POST"])
def add_arxiv():
    """添加arXiv论文"""
    data = request.get_json()
    arxiv_id = data.get("arxiv_id", "").strip()

    if not arxiv_id:
        return jsonify({"error": "ArXiv ID required"}), 400

    paper_info = fetch_arxiv_paper(arxiv_id)
    if paper_info:
        # 检查是否重复
        duplicate_paper = db_manager.check_duplicate_paper(
            paper_info.get("title", ""),
            paper_info.get("authors", "")
        )

        if duplicate_paper:
            # 删除新下载的文件（如果存在）
            if paper_info.get("file_path") and os.path.exists(paper_info["file_path"]):
                try:
                    os.remove(paper_info["file_path"])
                    print(
                        f"检测到重复论文，已删除新下载的文件: {paper_info['file_path']}"
                    )
                except Exception as e:
                    print(f"删除重复文件失败: {e}")

            return (
                jsonify(
                    {
                        "success": False,
                        "error": "检测到重复论文",
                        "duplicate_paper": {
                            "id": duplicate_paper["id"],
                            "title": duplicate_paper["title"],
                            "authors": duplicate_paper["authors"],
                            "source": duplicate_paper["source"],
                        },
                        "message": f'论文 "{duplicate_paper["title"]}" 已存在于论文库中',
                    }
                ),
                409,
            )  # 409 Conflict

        # 保存到数据库
        paper_id = db_manager.add_paper(paper_info)
        paper_info["id"] = paper_id

        return jsonify({"success": True, "paper": paper_info})
    else:
        return jsonify({"error": "Failed to fetch arXiv paper"}), 400


@app.route("/delete_paper/<int:paper_id>", methods=["DELETE"])
def delete_paper(paper_id):
    """删除论文"""
    try:
        # 获取要删除的论文信息
        paper_to_delete = db_manager.get_paper(paper_id)

        if not paper_to_delete:
            return jsonify({"error": "论文不存在"}), 404

        # 删除PDF文件（如果存在）
        if paper_to_delete.get("file_path") and os.path.exists(
            paper_to_delete["file_path"]
        ):
            try:
                os.remove(paper_to_delete["file_path"])
                print(f"已删除PDF文件: {paper_to_delete['file_path']}")
            except Exception as e:
                print(f"删除PDF文件失败: {e}")
                # 即使文件删除失败，也继续删除数据库记录

        # 从数据库删除论文记录
        if db_manager.delete_paper(paper_id):
            return jsonify(
                {
                    "success": True,
                    "message": "论文删除成功",
                    "deleted_paper": {
                        "id": paper_to_delete["id"],
                        "title": paper_to_delete["title"],
                    },
                }
            )
        else:
            return jsonify({"error": "数据库删除失败"}), 500

    except Exception as e:
        print(f"删除论文错误: {e}")
        return (
            jsonify({"error": "删除论文时发生错误，请稍后重试", "success": False}),
            500,
        )


@app.route("/chat/<int:paper_id>")
def chat_with_paper(paper_id):
    """与特定论文聊天"""
    paper = db_manager.get_paper(paper_id)

    if not paper:
        return redirect(url_for("index"))

    return render_template("chat.html", paper=paper)


@app.route("/view_pdf/<int:paper_id>")
def view_pdf(paper_id):
    """查看论文PDF文件"""
    paper = db_manager.get_paper(paper_id)

    if not paper or not paper.get("file_path"):
        return "PDF文件不存在", 404

    try:
        return send_file(paper["file_path"], mimetype="application/pdf")
    except Exception as e:
        print(f"PDF文件读取错误: {e}")
        return "PDF文件读取失败", 500


@app.route("/chat_api", methods=["POST"])
def chat_api():
    """聊天API - 与论文进行智能对话"""
    data = request.get_json()
    message = data.get("message", "").strip()
    paper_id = data.get("paper_id")
    session_id = data.get("session_id")  # 会话ID
    user_id = data.get("user_id", 1)  # 默认用户ID

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not paper_id:
        return jsonify({"error": "缺少论文ID"}), 400

    try:
        # 获取论文信息
        paper = db_manager.get_paper(paper_id)

        if not paper:
            return jsonify({"error": "论文不存在"}), 404

        # 获取或创建聊天会话
        if not session_id:
            session_id = db_manager.create_chat_session(paper_id, user_id)

        # 获取历史对话
        chat_messages = db_manager.get_recent_chat_messages(session_id, limit=10)

        # 构建对话消息列表
        messages = []
        for chat_msg in chat_messages:
            messages.append({
                "role": chat_msg["role"],
                "content": chat_msg["content"]
            })

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        # 保存用户消息到数据库
        db_manager.add_chat_message(session_id, "user", message)

        # 调用RAG增强的LLM API
        response_text = call_llm_api_with_rag(messages, paper_id, paper_context=paper)

        # 保存AI回复到数据库
        db_manager.add_chat_message(session_id, "assistant", response_text)

        return jsonify(
            {
                "success": True,
                "response": response_text,
                "paper_title": paper.get("title", ""),
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        print(f"聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


@app.route("/chat_stream", methods=["POST"])
def chat_stream():
    """流式聊天API - 与论文进行智能对话"""
    data = request.get_json()
    message = data.get("message", "").strip()
    paper_id = data.get("paper_id")
    session_id = data.get("session_id")
    user_id = data.get("user_id", 1)

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not paper_id:
        return jsonify({"error": "缺少论文ID"}), 400

    try:
        # 获取论文信息
        paper = db_manager.get_paper(paper_id)

        if not paper:
            return jsonify({"error": "论文不存在"}), 404

        # 获取或创建聊天会话
        if not session_id:
            session_id = db_manager.create_chat_session(paper_id, user_id)

        # 获取历史对话
        chat_messages = db_manager.get_recent_chat_messages(session_id, limit=10)

        # 构建对话消息列表
        messages = []
        for chat_msg in chat_messages:
            messages.append({
                "role": chat_msg["role"],
                "content": chat_msg["content"]
            })

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        # 保存用户消息到数据库
        db_manager.add_chat_message(session_id, "user", message)

        def generate():
            try:
                full_response = ""
                for chunk in call_llm_api_stream_with_rag(
                    messages, paper_id, paper_context=paper
                ):
                    full_response += chunk
                    # 发送每个数据块
                    yield f"data: {json.dumps({'content': chunk, 'type': 'content', 'session_id': session_id})}\n\n"

                # 保存完整的AI回复到数据库
                if full_response:
                    db_manager.add_chat_message(session_id, "assistant", full_response)

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done', 'session_id': session_id})}\n\n"

            except Exception as e:
                print(f"流式聊天错误: {e}")
                yield f"data: {json.dumps({'content': '抱歉，出现了错误，请稍后重试。', 'type': 'error'})}\n\n"
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

        return Response(
            generate(),
            mimetype="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        print(f"流式聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


@app.route("/generate_pre_read/<int:paper_id>", methods=["POST"])
def generate_pre_read(paper_id):
    """生成论文预阅读"""
    try:
        # 获取论文信息
        paper = db_manager.get_paper(paper_id)

        if not paper:
            return jsonify({"error": "论文不存在"}), 404

        # 检查是否有PDF文件
        if not paper.get("file_path") or not os.path.exists(paper["file_path"]):
            return jsonify({"error": "论文PDF文件不存在"}), 404

        # 检查是否已经有预阅读文件
        pre_read_filename = f"pre_read_{paper_id}.md"
        pre_read_path = os.path.join("pre_reads", pre_read_filename)

        if os.path.exists(pre_read_path):
            return jsonify({"success": True, "message": "预阅读已存在", "exists": True})

        # 读取PDF内容
        pdf_content = ""
        try:
            with open(paper["file_path"], "rb") as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    pdf_content += page.extract_text()
        except Exception as e:
            print(f"PDF读取错误: {e}")
            return jsonify({"error": "PDF文件读取失败"}), 500

        # 读取预阅读提示词
        try:
            with open("static/pre_reads_prompt.md", "r", encoding="utf-8") as f:
                prompt_content = f.read()
        except Exception as e:
            print(f"提示词文件读取错误: {e}")
            return jsonify({"error": "预阅读提示词文件读取失败"}), 500

        # 调用预阅读API
        pre_read_result = call_pre_read_api(pdf_content, prompt_content)

        # 保存预阅读结果
        try:
            with open(pre_read_path, "w", encoding="utf-8") as f:
                f.write(pre_read_result)
        except Exception as e:
            print(f"预阅读文件保存错误: {e}")
            return jsonify({"error": "预阅读结果保存失败"}), 500

        # 删除隐藏预阅读（如果存在）
        rag_svc = get_rag_service()
        if rag_svc:
            rag_svc.delete_hidden_pre_read(paper_id)
            # 重新创建向量数据库（使用正式预阅读）
            rag_svc.create_vector_store_from_pre_read(paper_id, pre_read_result)

        return jsonify(
            {
                "success": True,
                "message": "预阅读生成成功",
                "pre_read_path": pre_read_filename,
            }
        )

    except Exception as e:
        print(f"预阅读生成错误: {e}")
        return (
            jsonify({"error": "预阅读生成时发生错误，请稍后重试", "success": False}),
            500,
        )


@app.route("/view_pre_read/<int:paper_id>")
def view_pre_read(paper_id):
    """查看论文预阅读"""
    try:
        # 获取论文信息
        paper = db_manager.get_paper(paper_id)

        if not paper:
            return redirect(url_for("index"))

        # 检查预阅读文件是否存在
        pre_read_filename = f"pre_read_{paper_id}.md"
        pre_read_path = os.path.join("pre_reads", pre_read_filename)

        pre_read_content = ""
        pre_read_exists = False

        if os.path.exists(pre_read_path):
            try:
                with open(pre_read_path, "r", encoding="utf-8") as f:
                    pre_read_content = f.read()
                pre_read_exists = True
            except Exception as e:
                print(f"预阅读文件读取错误: {e}")
                pre_read_content = "预阅读文件读取失败"

        return render_template(
            "pre_read.html",
            paper=paper,
            pre_read_content=pre_read_content,
            pre_read_exists=pre_read_exists,
        )

    except Exception as e:
        print(f"预阅读查看错误: {e}")
        return redirect(url_for("index"))


@app.route("/api/papers", methods=["GET"])
def api_get_papers():
    """获取论文列表API"""
    try:
        papers = db_manager.get_all_papers()

        # 为每篇论文添加预阅读状态信息
        for paper in papers:
            # 检查是否有正式预阅读
            formal_pre_read_path = os.path.join(
                "pre_reads", f"pre_read_{paper['id']}.md"
            )
            paper["has_formal_preread"] = os.path.exists(formal_pre_read_path)

            # 检查是否有隐藏预阅读
            hidden_pre_read_path = os.path.join(
                "hidden_pre_reads", f"hidden_pre_read_{paper['id']}.md"
            )
            paper["has_hidden_preread"] = os.path.exists(hidden_pre_read_path)

        return jsonify({"success": True, "papers": papers})

    except Exception as e:
        print(f"获取论文列表错误: {e}")
        return jsonify({"error": "获取论文列表失败"}), 500


@app.route("/api/start_multi_doc_analysis", methods=["POST"])
def api_start_multi_doc_analysis():
    """初始化跨文档分析API"""
    try:
        data = request.get_json()
        main_paper_id = data.get("main_paper_id")
        reference_paper_ids = data.get("reference_paper_ids", [])

        if not main_paper_id:
            return jsonify({"error": "缺少主论文ID"}), 400

        if not reference_paper_ids:
            return jsonify({"error": "请至少选择一篇参考论文"}), 400

        # 验证论文是否存在
        main_paper = db_manager.get_paper(main_paper_id)
        if not main_paper:
            return jsonify({"error": "主论文不存在"}), 404

        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = db_manager.get_paper(ref_id)
            if ref_paper:
                reference_papers.append(ref_paper)
            else:
                return jsonify({"error": f"参考论文ID {ref_id} 不存在"}), 404

        # 获取RAG服务
        rag_svc = get_rag_service()
        if not rag_svc:
            return jsonify({"error": "RAG服务不可用"}), 500

        # 确保所有参考论文都有预阅读（隐藏或正式）
        processed_papers = []
        for ref_paper in reference_papers:
            # 调用RAG服务确保预阅读存在
            if rag_svc.get_or_create_rag_context(ref_paper["id"], ref_paper):
                processed_papers.append(ref_paper["id"])
            else:
                return (
                    jsonify({"error": f'论文 {ref_paper["title"]} 的预阅读生成失败'}),
                    500,
                )

        # 同样确保主论文有预阅读
        if not rag_svc.get_or_create_rag_context(main_paper_id, main_paper):
            return jsonify({"error": "主论文的预阅读生成失败"}), 500

        return jsonify(
            {
                "success": True,
                "main_paper_id": main_paper_id,
                "reference_paper_ids": processed_papers,
                "message": f"跨文档分析初始化成功，包含 {len(processed_papers) + 1} 篇论文",
            }
        )

    except Exception as e:
        print(f"跨文档分析初始化错误: {e}")
        return jsonify({"error": "初始化失败，请稍后重试"}), 500


@app.route("/chat_multi_doc_api", methods=["POST"])
def chat_multi_doc_api():
    """跨文档分析聊天API"""
    data = request.get_json()
    message = data.get("message", "").strip()
    main_paper_id = data.get("main_paper_id")
    reference_paper_ids = data.get("reference_paper_ids", [])
    chat_history = data.get("chat_history", [])

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not main_paper_id:
        return jsonify({"error": "缺少主论文ID"}), 400

    try:
        # 获取论文信息
        main_paper = db_manager.get_paper(main_paper_id)

        if not main_paper:
            return jsonify({"error": "主论文不存在"}), 404

        # 获取参考论文信息
        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = db_manager.get_paper(ref_id)
            if ref_paper:
                reference_papers.append(ref_paper)

        # 构建对话消息列表
        messages = []

        # 添加历史对话
        for history_item in chat_history[-10:]:
            if history_item.get("role") and history_item.get("content"):
                messages.append(
                    {"role": history_item["role"], "content": history_item["content"]}
                )

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        # 调用多文档RAG增强的LLM API
        response_text = call_llm_api_with_multi_doc_rag(
            messages, main_paper_id, reference_paper_ids, main_paper, reference_papers
        )

        return jsonify(
            {
                "success": True,
                "response": response_text,
                "main_paper_title": main_paper.get("title", ""),
                "reference_papers_count": len(reference_papers),
                "timestamp": datetime.now().isoformat(),
            }
        )

    except Exception as e:
        print(f"跨文档聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


def call_llm_api_stream_with_multi_doc_rag(
    messages, main_paper_id, reference_paper_ids, main_paper, reference_papers
):
    """调用LLM API进行多文档RAG增强的流式对话"""
    try:
        # 获取用户最新的问题
        user_message = ""
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return

        # 获取RAG服务
        rag_svc = get_rag_service()
        if not rag_svc:
            print(f"RAG服务不可用，回退到普通对话模式")
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return

        # 确保所有论文的RAG上下文存在
        if not rag_svc.get_or_create_rag_context(main_paper_id, main_paper):
            print(f"主论文RAG上下文创建失败，回退到普通对话模式")
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return

        # 确保参考论文的RAG上下文存在
        for ref_id, ref_paper in zip(reference_paper_ids, reference_papers):
            if not rag_svc.get_or_create_rag_context(ref_id, ref_paper):
                print(f"参考论文 {ref_id} RAG上下文创建失败")

        # 进行多文档搜索
        relevant_docs = rag_svc.search_multi_doc_relevant_documents(
            main_paper_id,
            reference_paper_ids,
            user_message,
            k=8,  # 多文档模式下获取更多相关内容
        )

        if not relevant_docs:
            print(f"未找到相关文档，回退到普通对话模式")
            for chunk in call_llm_api_stream(messages, main_paper):
                yield chunk
            return

        # 创建多文档RAG增强的提示词
        rag_prompt = rag_svc.create_multi_doc_rag_prompt(
            user_message, relevant_docs, main_paper, reference_papers
        )

        # 构建RAG对话消息
        rag_messages = []

        # 添加历史对话（除了最后一条用户消息）
        for msg in messages[:-1]:
            if msg.get("role") and msg.get("content"):
                rag_messages.append(msg)

        # 添加RAG增强的用户消息
        rag_messages.append({"role": "user", "content": rag_prompt})

        # 调用流式LLM API
        llm_config = CONFIG.get("llm", {})

        headers = {
            "Authorization": f'Bearer {llm_config.get("api_key", "")}',
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "DocuMancer",
        }

        payload = {
            "model": llm_config.get("model", "google/gemini-2.5-flash"),
            "messages": rag_messages,
            "max_tokens": llm_config.get("max_tokens", 32768),
            "temperature": llm_config.get("temperature", 0.7),
            "stream": True,
        }

        print(f"正在调用多文档RAG流式LLM API: {llm_config.get('model')}")

        response = requests.post(
            f"{llm_config.get('base_url', 'https://openrouter.ai/api/v1')}/chat/completions",
            headers=headers,
            json=payload,
            timeout=60,
            stream=True,
        )

        if response.status_code == 200:
            # 返回流式响应的生成器
            for line in response.iter_lines():
                if line:
                    line_str = line.decode("utf-8")
                    if line_str.startswith("data: "):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        if data_str.strip() == "[DONE]":
                            break
                        try:
                            data = json.loads(data_str)
                            if "choices" in data and len(data["choices"]) > 0:
                                delta = data["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
        else:
            print(f"多文档RAG流式API调用失败: {response.status_code}, {response.text}")
            yield f"AI服务暂时不可用 (错误代码: {response.status_code})，请稍后再试。"

    except Exception as e:
        print(f"多文档RAG流式LLM API调用错误: {e}")
        yield "抱歉，AI助手遇到了技术问题，请稍后再试。"


@app.route("/chat_multi_doc_stream", methods=["POST"])
def chat_multi_doc_stream():
    """跨文档分析流式聊天API"""
    data = request.get_json()
    message = data.get("message", "").strip()
    main_paper_id = data.get("main_paper_id")
    reference_paper_ids = data.get("reference_paper_ids", [])
    chat_history = data.get("chat_history", [])

    if not message:
        return jsonify({"error": "消息不能为空"}), 400

    if not main_paper_id:
        return jsonify({"error": "缺少主论文ID"}), 400

    try:
        # 获取论文信息
        papers = load_papers()
        main_paper = next((p for p in papers if p["id"] == main_paper_id), None)

        if not main_paper:
            return jsonify({"error": "主论文不存在"}), 404

        # 获取参考论文信息
        reference_papers = []
        for ref_id in reference_paper_ids:
            ref_paper = next((p for p in papers if p["id"] == ref_id), None)
            if ref_paper:
                reference_papers.append(ref_paper)

        # 构建对话消息列表
        messages = []

        # 添加历史对话
        for history_item in chat_history[-10:]:
            if history_item.get("role") and history_item.get("content"):
                messages.append(
                    {"role": history_item["role"], "content": history_item["content"]}
                )

        # 添加当前用户消息
        messages.append({"role": "user", "content": message})

        def generate():
            try:
                for chunk in call_llm_api_stream_with_multi_doc_rag(
                    messages,
                    main_paper_id,
                    reference_paper_ids,
                    main_paper,
                    reference_papers,
                ):
                    # 发送每个数据块
                    yield f"data: {json.dumps({'content': chunk, 'type': 'content'})}\n\n"

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

            except Exception as e:
                print(f"跨文档流式聊天错误: {e}")
                yield f"data: {json.dumps({'content': '抱歉，出现了错误，请稍后重试。', 'type': 'error'})}\n\n"
                yield f"data: {json.dumps({'type': 'done'})}\n\n"

        return Response(
            generate(),
            mimetype="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Content-Type",
            },
        )

    except Exception as e:
        print(f"跨文档流式聊天API错误: {e}")
        return jsonify({"error": "服务器内部错误，请稍后重试", "success": False}), 500


@app.route("/api/switch_current_paper", methods=["POST"])
def api_switch_current_paper():
    """切换当前论文API"""
    try:
        data = request.get_json()
        new_current_paper_id = data.get("paper_id")

        if not new_current_paper_id:
            return jsonify({"error": "缺少论文ID"}), 400

        # 验证论文是否存在
        papers = load_papers()
        target_paper = next(
            (p for p in papers if p["id"] == new_current_paper_id), None
        )
        if not target_paper:
            return jsonify({"error": "论文不存在"}), 404

        # 确保论文有预阅读
        rag_svc = get_rag_service()
        if rag_svc and not rag_svc.get_or_create_rag_context(
            new_current_paper_id, target_paper
        ):
            return jsonify({"error": "论文的预阅读生成失败"}), 500

        return jsonify(
            {
                "success": True,
                "current_paper_id": new_current_paper_id,
                "paper_info": {
                    "id": target_paper["id"],
                    "title": target_paper["title"],
                    "authors": target_paper["authors"],
                    "source": target_paper["source"],
                    "has_pdf": target_paper.get("has_pdf", True),
                },
                "message": f'已切换到论文：{target_paper["title"]}',
            }
        )

    except Exception as e:
        print(f"切换当前论文错误: {e}")
        return jsonify({"error": "切换失败，请稍后重试"}), 500


@app.route("/api/clean_duplicates", methods=["POST"])
def api_clean_duplicates():
    """清理现有论文库中的重复论文"""
    try:
        papers = db_manager.get_all_papers()
        original_count = len(papers)
        removed_count = 0

        # 检查重复论文并删除
        seen_papers = set()
        for paper in papers:
            # 创建唯一标识符
            identifier = f"{paper.get('title', '').lower().strip()}:{paper.get('authors', '').lower().strip()}"

            if identifier in seen_papers:
                # 发现重复，删除这篇论文
                if db_manager.delete_paper(paper["id"]):
                    removed_count += 1
                    print(f"删除重复论文: {paper.get('title', '未知')}")
            else:
                seen_papers.add(identifier)

        remaining_count = original_count - removed_count

        if removed_count > 0:
            return jsonify(
                {
                    "success": True,
                    "message": f"成功清理重复论文，从 {original_count} 篇减少到 {remaining_count} 篇",
                    "removed_count": removed_count,
                    "remaining_count": remaining_count,
                }
            )
        else:
            return jsonify(
                {
                    "success": True,
                    "message": "未发现重复论文",
                    "removed_count": 0,
                    "remaining_count": original_count,
                }
            )

    except Exception as e:
        print(f"清理重复论文错误: {e}")
        return jsonify({"error": "清理重复论文时发生错误", "success": False}), 500


@app.route("/api/chat_sessions/<int:paper_id>", methods=["GET"])
def get_chat_sessions(paper_id):
    """获取论文的聊天会话列表"""
    try:
        sessions = db_manager.get_paper_chat_sessions(paper_id)
        return jsonify({"success": True, "sessions": sessions})
    except Exception as e:
        print(f"获取聊天会话错误: {e}")
        return jsonify({"error": "获取聊天会话失败"}), 500


@app.route("/api/chat_messages/<int:session_id>", methods=["GET"])
def get_chat_messages(session_id):
    """获取会话的聊天消息"""
    try:
        limit = request.args.get("limit", 50, type=int)
        messages = db_manager.get_chat_messages(session_id, limit)
        return jsonify({"success": True, "messages": messages})
    except Exception as e:
        print(f"获取聊天消息错误: {e}")
        return jsonify({"error": "获取聊天消息失败"}), 500


@app.route("/api/chat_sessions/<int:session_id>", methods=["DELETE"])
def delete_chat_session(session_id):
    """删除聊天会话"""
    try:
        if db_manager.delete_chat_session(session_id):
            return jsonify({"success": True, "message": "会话删除成功"})
        else:
            return jsonify({"error": "会话不存在"}), 404
    except Exception as e:
        print(f"删除聊天会话错误: {e}")
        return jsonify({"error": "删除会话失败"}), 500


@app.route("/api/users", methods=["POST"])
def create_user():
    """创建新用户"""
    try:
        data = request.get_json()
        username = data.get("username", "").strip()
        email = data.get("email", "").strip()

        if not username:
            return jsonify({"error": "用户名不能为空"}), 400

        # 检查用户名是否已存在
        existing_user = db_manager.get_user_by_username(username)
        if existing_user:
            return jsonify({"error": "用户名已存在"}), 409

        user_id = db_manager.add_user(username, email if email else None)
        user = db_manager.get_user(user_id)

        return jsonify({"success": True, "user": user})
    except Exception as e:
        print(f"创建用户错误: {e}")
        return jsonify({"error": "创建用户失败"}), 500


@app.route("/api/users/<username>", methods=["GET"])
def get_user_by_username(username):
    """根据用户名获取用户信息"""
    try:
        user = db_manager.get_user_by_username(username)
        if user:
            return jsonify({"success": True, "user": user})
        else:
            return jsonify({"error": "用户不存在"}), 404
    except Exception as e:
        print(f"获取用户错误: {e}")
        return jsonify({"error": "获取用户失败"}), 500


@app.route("/api/chat_sessions", methods=["POST"])
def create_chat_session():
    """创建新的聊天会话"""
    try:
        data = request.get_json()
        paper_id = data.get("paper_id")
        user_id = data.get("user_id", 1)
        session_name = data.get("session_name")

        if not paper_id:
            return jsonify({"error": "缺少论文ID"}), 400

        session_id = db_manager.create_chat_session(paper_id, user_id, session_name)
        session = db_manager.get_chat_session(session_id)

        return jsonify({"success": True, "session": session})
    except Exception as e:
        print(f"创建聊天会话错误: {e}")
        return jsonify({"error": "创建会话失败"}), 500


@app.route("/api/chat_sessions/<int:paper_id>/list", methods=["GET"])
def list_chat_sessions(paper_id):
    """获取论文的所有聊天会话"""
    try:
        sessions = db_manager.get_paper_chat_sessions(paper_id)
        return jsonify({"success": True, "sessions": sessions})
    except Exception as e:
        print(f"获取聊天会话列表错误: {e}")
        return jsonify({"error": "获取会话列表失败"}), 500


@app.route("/api/chat_sessions/<int:session_id>/messages", methods=["GET"])
def get_session_messages(session_id):
    """获取会话的所有消息"""
    try:
        limit = request.args.get("limit", type=int)
        messages = db_manager.get_chat_messages(session_id, limit)
        return jsonify({"success": True, "messages": messages})
    except Exception as e:
        print(f"获取会话消息错误: {e}")
        return jsonify({"error": "获取消息失败"}), 500


@app.route("/api/chat_sessions/<int:session_id>/rename", methods=["PUT"])
def rename_chat_session(session_id):
    """重命名聊天会话"""
    try:
        data = request.get_json()
        new_name = data.get("session_name", "").strip()

        if not new_name:
            return jsonify({"error": "会话名称不能为空"}), 400

        with db_manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE chat_sessions
                SET session_name = ?
                WHERE id = ?
            """, (new_name, session_id))
            conn.commit()

            if cursor.rowcount > 0:
                return jsonify({"success": True, "message": "会话重命名成功"})
            else:
                return jsonify({"error": "会话不存在"}), 404

    except Exception as e:
        print(f"重命名会话错误: {e}")
        return jsonify({"error": "重命名失败"}), 500


@app.route("/api/database_stats", methods=["GET"])
def get_database_stats():
    """获取数据库统计信息"""
    try:
        stats = db_manager.get_database_stats()
        return jsonify({"success": True, "stats": stats})
    except Exception as e:
        print(f"获取数据库统计错误: {e}")
        return jsonify({"error": "获取统计信息失败"}), 500


# 海报生成状态跟踪
poster_generation_status = {}


def run_poster_generation(paper_id, paper_path, output_dir):
    """在后台运行海报生成"""
    try:
        poster_generation_status[paper_id] = {
            "status": "running",
            "progress": "正在初始化...",
            "start_time": datetime.now().isoformat()
        }

        poster_generation_status[paper_id]["progress"] = "正在生成海报..."

        # 使用包装器脚本生成海报
        cmd = [
            "python", "poster_generator.py",
            paper_path,
            str(paper_id),
            "48",  # width
            "36"   # height
        ]

        # 运行海报生成
        result = subprocess.run(
            cmd,
            cwd=os.getcwd(),
            capture_output=True,
            text=True,
            timeout=1800  # 30分钟超时
        )

        if result.returncode == 0:
            try:
                # 解析结果
                result_data = json.loads(result.stdout)
                if result_data.get("success"):
                    poster_generation_status[paper_id] = {
                        "status": "completed",
                        "progress": "海报生成完成",
                        "output_dir": result_data.get("output_dir", output_dir),
                        "end_time": datetime.now().isoformat()
                    }
                else:
                    poster_generation_status[paper_id] = {
                        "status": "error",
                        "progress": "海报生成失败",
                        "error": result_data.get("error", "Unknown error"),
                        "end_time": datetime.now().isoformat()
                    }
            except json.JSONDecodeError:
                poster_generation_status[paper_id] = {
                    "status": "error",
                    "progress": "海报生成结果解析失败",
                    "error": f"Invalid JSON output: {result.stdout}",
                    "end_time": datetime.now().isoformat()
                }
        else:
            poster_generation_status[paper_id] = {
                "status": "error",
                "progress": "海报生成失败",
                "error": f"Exit code {result.returncode}: {result.stderr}",
                "end_time": datetime.now().isoformat()
            }

    except subprocess.TimeoutExpired:
        poster_generation_status[paper_id] = {
            "status": "error",
            "progress": "海报生成超时",
            "error": "Generation timeout after 30 minutes",
            "end_time": datetime.now().isoformat()
        }
    except Exception as e:
        poster_generation_status[paper_id] = {
            "status": "error",
            "progress": "海报生成出错",
            "error": str(e),
            "end_time": datetime.now().isoformat()
        }


@app.route("/api/generate_poster/<int:paper_id>", methods=["POST"])
def generate_poster(paper_id):
    """生成论文海报"""
    try:
        # 获取论文信息
        paper = db_manager.get_paper(paper_id)

        if not paper:
            return jsonify({"error": "论文不存在"}), 404

        # 检查是否有PDF文件
        if not paper.get("file_path") or not os.path.exists(paper["file_path"]):
            return jsonify({"error": "论文PDF文件不存在"}), 404

        # 检查是否已经在生成中
        if paper_id in poster_generation_status:
            status = poster_generation_status[paper_id]
            if status["status"] == "running":
                return jsonify({
                    "success": False,
                    "message": "海报正在生成中",
                    "status": status
                })

        # 创建输出目录
        output_dir = os.path.join("posters", f"paper_{paper_id}")
        os.makedirs(output_dir, exist_ok=True)

        # 检查Paper2Poster是否存在
        paper2poster_dir = os.path.join(os.getcwd(), "Paper2Poster")
        if not os.path.exists(paper2poster_dir):
            return jsonify({"error": "Paper2Poster模块未找到"}), 500

        # 在后台线程中运行海报生成
        thread = threading.Thread(
            target=run_poster_generation,
            args=(paper_id, paper["file_path"], output_dir)
        )
        thread.daemon = True
        thread.start()

        return jsonify({
            "success": True,
            "message": "海报生成已开始",
            "paper_id": paper_id,
            "status": "started"
        })

    except Exception as e:
        print(f"海报生成错误: {e}")
        return jsonify({"error": "海报生成启动失败"}), 500


@app.route("/api/poster_status/<int:paper_id>", methods=["GET"])
def get_poster_status(paper_id):
    """获取海报生成状态"""
    try:
        if paper_id not in poster_generation_status:
            return jsonify({
                "success": True,
                "status": "not_started",
                "message": "尚未开始生成海报"
            })

        status = poster_generation_status[paper_id]
        return jsonify({
            "success": True,
            "status": status["status"],
            "progress": status["progress"],
            "start_time": status.get("start_time"),
            "end_time": status.get("end_time"),
            "error": status.get("error"),
            "output_dir": status.get("output_dir")
        })

    except Exception as e:
        print(f"获取海报状态错误: {e}")
        return jsonify({"error": "获取状态失败"}), 500


@app.route("/api/poster_files/<int:paper_id>", methods=["GET"])
def get_poster_files(paper_id):
    """获取生成的海报文件列表"""
    try:
        output_dir = os.path.join("posters", f"paper_{paper_id}")

        if not os.path.exists(output_dir):
            return jsonify({
                "success": True,
                "files": [],
                "message": "海报文件不存在"
            })

        files = []
        for root, _, filenames in os.walk(output_dir):
            for filename in filenames:
                file_path = os.path.join(root, filename)
                rel_path = os.path.relpath(file_path, output_dir)
                file_size = os.path.getsize(file_path)
                file_ext = os.path.splitext(filename)[1].lower()

                files.append({
                    "name": filename,
                    "path": rel_path,
                    "size": file_size,
                    "type": file_ext,
                    "is_image": file_ext in ['.png', '.jpg', '.jpeg'],
                    "is_pptx": file_ext == '.pptx'
                })

        return jsonify({
            "success": True,
            "files": files,
            "total_files": len(files)
        })

    except Exception as e:
        print(f"获取海报文件错误: {e}")
        return jsonify({"error": "获取文件列表失败"}), 500


@app.route("/api/download_poster/<int:paper_id>/<path:filename>", methods=["GET"])
def download_poster_file(paper_id, filename):
    """下载海报文件"""
    try:
        output_dir = os.path.join("posters", f"paper_{paper_id}")
        file_path = os.path.join(output_dir, filename)

        # 安全检查：确保文件在指定目录内
        if not os.path.abspath(file_path).startswith(os.path.abspath(output_dir)):
            return jsonify({"error": "非法文件路径"}), 400

        if not os.path.exists(file_path):
            return jsonify({"error": "文件不存在"}), 404

        return send_file(file_path, as_attachment=True)

    except Exception as e:
        print(f"下载海报文件错误: {e}")
        return jsonify({"error": "文件下载失败"}), 500


if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=5000)
