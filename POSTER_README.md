# Paper2Poster 海报生成功能

本功能集成了Paper2Poster项目，为DocuMancer添加了自动生成论文海报的能力。

## 功能特性

- 🎨 自动从PDF论文生成专业海报
- 📊 支持PowerPoint格式输出
- 🖼️ 生成高质量图片预览
- ⚡ 后台异步处理
- 📱 实时状态更新
- 💾 文件下载管理

## 安装配置

### 1. 安装依赖

运行安装脚本：
```bash
./install_poster_deps.sh
```

或手动安装关键依赖：
```bash
pip install python-pptx PyPDF2 Pillow torch transformers docling
```

### 2. 配置Paper2Poster

确保Paper2Poster目录结构正确：
```
DocuMancer/
├── Paper2Poster/
│   ├── PosterAgent/
│   │   ├── new_pipeline.py
│   │   └── ...
│   ├── utils/
│   └── requirements.txt
├── poster_generator.py
└── app.py
```

## 使用方法

### 通过Web界面

1. 上传PDF论文到DocuMancer
2. 在论文卡片中点击"生成海报"按钮
3. 等待生成完成（通常需要5-15分钟）
4. 点击"查看海报"下载生成的文件

### 通过API

#### 开始生成海报
```http
POST /api/generate_poster/<paper_id>
```

#### 查询生成状态
```http
GET /api/poster_status/<paper_id>
```

#### 获取文件列表
```http
GET /api/poster_files/<paper_id>
```

#### 下载文件
```http
GET /api/download_poster/<paper_id>/<filename>
```

### 命令行使用

```bash
python poster_generator.py <pdf_path> <paper_id> [width] [height]
```

参数说明：
- `pdf_path`: PDF文件路径
- `paper_id`: 论文ID（用于输出目录命名）
- `width`: 海报宽度（英寸，默认48）
- `height`: 海报高度（英寸，默认36）

## 输出文件

生成的海报文件保存在 `posters/paper_<id>/` 目录下，包含：

- `*.pptx`: PowerPoint演示文件
- `*.png`: 海报图片文件
- `log.json`: 生成日志
- `detail_log.json`: 详细日志

## 状态说明

- `not_started`: 尚未开始
- `running`: 正在生成
- `completed`: 生成完成
- `error`: 生成失败

## 故障排除

### 常见问题

1. **依赖缺失**
   ```bash
   # 重新安装依赖
   ./install_poster_deps.sh
   ```

2. **生成超时**
   - 检查PDF文件大小（建议<50MB）
   - 确保系统内存充足（建议>8GB）

3. **模型下载失败**
   ```bash
   # 手动下载模型
   python -c "from transformers import AutoModel; AutoModel.from_pretrained('sentence-transformers/all-MiniLM-L6-v2')"
   ```

4. **权限问题**
   ```bash
   # 确保目录权限
   chmod -R 755 Paper2Poster/
   chmod -R 755 posters/
   ```

### 日志查看

生成过程的详细日志保存在：
- 应用日志：控制台输出
- 生成日志：`posters/paper_<id>/log.json`

## 性能优化

- 使用GPU加速（如果可用）
- 调整海报尺寸以平衡质量和速度
- 定期清理临时文件

## 技术架构

```
Web界面 → Flask API → poster_generator.py → Paper2Poster → 输出文件
```

## 注意事项

1. 海报生成是计算密集型任务，建议在服务器环境运行
2. 首次运行会下载AI模型，需要网络连接
3. 生成时间取决于论文复杂度和硬件性能
4. 建议定期清理生成的海报文件以节省存储空间

## 更新日志

- v1.0: 基础海报生成功能
- v1.1: 添加状态跟踪和文件管理
- v1.2: 优化错误处理和用户体验
