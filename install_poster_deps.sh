#!/bin/bash

# Paper2Poster依赖安装脚本

echo "开始安装Paper2Poster依赖..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
echo "检测到Python版本: $python_version"

# 检查Paper2Poster目录是否存在
if [ ! -d "Paper2Poster" ]; then
    echo "错误: Paper2Poster目录不存在"
    echo "请确保Paper2Poster代码已正确放置在当前目录下"
    exit 1
fi

# 安装基础依赖
echo "安装基础Python包..."
pip3 install --upgrade pip

# 安装Paper2Poster核心依赖
echo "安装Paper2Poster核心依赖..."
pip3 install python-pptx PyPDF2 Pillow requests numpy pandas matplotlib

# 安装机器学习相关依赖
echo "安装机器学习依赖..."
pip3 install torch torchvision transformers sentence-transformers

# 安装文档处理依赖
echo "安装文档处理依赖..."
pip3 install docling docling-core docling-parse

# 安装其他必要依赖
echo "安装其他依赖..."
pip3 install opencv-python scikit-learn scipy

# 检查关键包是否安装成功
echo "检查关键依赖..."
python3 -c "import torch; print('PyTorch安装成功')" || echo "警告: PyTorch安装失败"
python3 -c "import transformers; print('Transformers安装成功')" || echo "警告: Transformers安装失败"
python3 -c "from pptx import Presentation; print('python-pptx安装成功')" || echo "警告: python-pptx安装失败"
python3 -c "import docling; print('Docling安装成功')" || echo "警告: Docling安装失败"

echo "Paper2Poster依赖安装完成！"
echo "注意: 如果有警告信息，请手动安装对应的包"
echo ""
echo "测试海报生成功能:"
echo "python3 poster_generator.py <pdf_path> <paper_id>"
