#!/usr/bin/env python3
"""
Paper2Poster包装器
用于生成论文海报的独立脚本
"""

import os
import sys
import subprocess
import shutil
import tempfile
import json
from pathlib import Path


class PosterGenerator:
    def __init__(self):
        self.paper2poster_dir = Path(__file__).parent / "Paper2Poster"
        self.output_base_dir = Path(__file__).parent / "posters"
        
    def check_dependencies(self):
        """检查Paper2Poster依赖"""
        if not self.paper2poster_dir.exists():
            raise FileNotFoundError("Paper2Poster目录不存在")
            
        pipeline_script = self.paper2poster_dir / "PosterAgent" / "new_pipeline.py"
        if not pipeline_script.exists():
            raise FileNotFoundError("Paper2Poster主脚本不存在")
            
        return True
        
    def setup_environment(self):
        """设置环境变量和配置"""
        # 设置Python路径
        paper2poster_path = str(self.paper2poster_dir)
        if paper2poster_path not in sys.path:
            sys.path.insert(0, paper2poster_path)
            
        # 设置环境变量
        os.environ['PYTHONPATH'] = f"{paper2poster_path}:{os.environ.get('PYTHONPATH', '')}"
        
    def prepare_paper_directory(self, pdf_path, paper_id):
        """为论文创建目录结构"""
        # 创建临时工作目录
        work_dir = self.paper2poster_dir / "tmp" / f"paper_{paper_id}"
        work_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制PDF文件到工作目录
        pdf_dest = work_dir / "paper.pdf"
        shutil.copy2(pdf_path, pdf_dest)
        
        return str(pdf_dest)
        
    def check_api_keys(self):
        """检查必要的API密钥"""
        import os

        # 检查OpenAI API密钥
        openai_key = os.environ.get('OPENAI_API_KEY')
        if not openai_key:
            return False, "缺少OPENAI_API_KEY环境变量"

        return True, "API密钥检查通过"

    def generate_poster(self, pdf_path, paper_id, width_inches=48, height_inches=36):
        """生成海报"""
        try:
            # 检查依赖
            self.check_dependencies()

            # 检查API密钥
            api_check, api_msg = self.check_api_keys()
            if not api_check:
                return {
                    "success": False,
                    "error": f"API配置错误: {api_msg}",
                    "suggestion": "请设置OPENAI_API_KEY环境变量或使用其他模型"
                }

            # 设置环境
            self.setup_environment()

            # 准备工作目录
            paper_pdf_path = self.prepare_paper_directory(pdf_path, paper_id)

            # 创建输出目录
            output_dir = self.output_base_dir / f"paper_{paper_id}"
            output_dir.mkdir(parents=True, exist_ok=True)

            # 构建命令 - 使用更稳定的模型配置
            cmd = [
                sys.executable,
                "PosterAgent/new_pipeline.py",
                "--poster_path", paper_pdf_path,
                "--model_name_t", "4o",
                "--model_name_v", "4o",
                "--tmp_dir", f"tmp/paper_{paper_id}",
                "--poster_width_inches", str(width_inches),
                "--poster_height_inches", str(height_inches),
                "--poster_name", f"paper_{paper_id}"
            ]
            
            print(f"运行命令: {' '.join(cmd)}")
            print(f"工作目录: {self.paper2poster_dir}")
            
            # 运行海报生成，添加更详细的错误处理
            print(f"开始生成海报，预计需要5-15分钟...")

            result = subprocess.run(
                cmd,
                cwd=self.paper2poster_dir,
                capture_output=True,
                text=True,
                timeout=900  # 15分钟超时，避免过长等待
            )

            print(f"海报生成进程完成，退出码: {result.returncode}")
            if result.stdout:
                print(f"标准输出: {result.stdout[:500]}...")
            if result.stderr:
                print(f"错误输出: {result.stderr[:500]}...")
            
            if result.returncode == 0:
                # 查找生成的文件
                generated_pattern = f"<4o_4o>_generated_posters"
                generated_dirs = list(self.paper2poster_dir.glob(generated_pattern))
                
                if generated_dirs:
                    generated_dir = generated_dirs[0]
                    # 移动生成的文件到输出目录
                    for item in generated_dir.iterdir():
                        if item.is_dir():
                            dest_dir = output_dir / item.name
                            if dest_dir.exists():
                                shutil.rmtree(dest_dir)
                            shutil.copytree(item, dest_dir)
                        else:
                            shutil.copy2(item, output_dir / item.name)
                    
                    # 清理临时文件
                    shutil.rmtree(generated_dir, ignore_errors=True)
                    
                    return {
                        "success": True,
                        "output_dir": str(output_dir),
                        "message": "海报生成成功"
                    }
                else:
                    return {
                        "success": False,
                        "error": "未找到生成的海报文件",
                        "stdout": result.stdout,
                        "stderr": result.stderr
                    }
            else:
                return {
                    "success": False,
                    "error": f"海报生成失败 (退出码: {result.returncode})",
                    "stdout": result.stdout,
                    "stderr": result.stderr
                }
                
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "海报生成超时 (30分钟)"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"海报生成出错: {str(e)}"
            }
        finally:
            # 清理临时工作目录
            work_dir = self.paper2poster_dir / "tmp" / f"paper_{paper_id}"
            if work_dir.exists():
                shutil.rmtree(work_dir, ignore_errors=True)


def main():
    """命令行入口"""
    if len(sys.argv) < 3:
        print("用法: python poster_generator.py <pdf_path> <paper_id> [width] [height]")
        sys.exit(1)
        
    pdf_path = sys.argv[1]
    paper_id = sys.argv[2]
    width = int(sys.argv[3]) if len(sys.argv) > 3 else 48
    height = int(sys.argv[4]) if len(sys.argv) > 4 else 36
    
    generator = PosterGenerator()
    result = generator.generate_poster(pdf_path, paper_id, width, height)
    
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if not result["success"]:
        sys.exit(1)


if __name__ == "__main__":
    main()
