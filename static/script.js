document.addEventListener('DOMContentLoaded', function() {
    // 获取页面元素
    const pdfUpload = document.getElementById('pdfUpload');
    const arxivInput = document.getElementById('arxivInput');
    const addArxivBtn = document.getElementById('addArxivBtn');
    const papersGrid = document.getElementById('papersGrid');
    const loadingOverlay = document.getElementById('loadingOverlay');

    // 使用事件委托处理删除按钮点击
    if (papersGrid) {
        papersGrid.addEventListener('click', function(e) {
            const deleteButton = e.target.closest('.delete-button');
            if (deleteButton) {
                const paperId = deleteButton.dataset.paperId;
                const paperTitle = deleteButton.dataset.paperTitle;
                deletePaper(paperId, paperTitle);
            }
        });
    }

    // PDF上传处理
    if (pdfUpload) {
        pdfUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                uploadPDF(file);
            } else {
                showNotification('请选择有效的PDF文件', 'error');
            }
        });
    }

    // arXiv添加处理
    if (addArxivBtn) {
        addArxivBtn.addEventListener('click', function() {
            const arxivId = arxivInput.value.trim();
            if (arxivId) {
                addArxivPaper(arxivId);
            } else {
                showNotification('请输入有效的arXiv ID', 'error');
            }
        });
    }

    // 点击外部关闭菜单
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.card-menu')) {
            const allMenus = document.querySelectorAll('.menu-dropdown');
            allMenus.forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // 回车键添加arXiv
    if (arxivInput) {
        arxivInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addArxivBtn.click();
            }
        });
    }

    // 上传PDF函数
    function uploadPDF(file) {
        showLoading('正在解析PDF...');
        
        const formData = new FormData();
        formData.append('pdf_file', file);

        fetch('/upload_pdf', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification('PDF上传并解析成功！', 'success');
                addPaperCard(data.paper);
                // 重置文件输入
                pdfUpload.value = '';
            } else {
                showNotification(data.error || 'PDF上传失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showNotification('PDF上传失败，请稍后重试', 'error');
        });
    }

    // 添加arXiv论文函数
    function addArxivPaper(arxivId) {
        showLoading('正在获取arXiv论文信息...');

        fetch('/add_arxiv', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ arxiv_id: arxivId })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification('arXiv论文添加成功！', 'success');
                addPaperCard(data.paper);
                arxivInput.value = '';
            } else {
                showNotification(data.error || 'arXiv论文添加失败', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showNotification('arXiv论文添加失败，请稍后重试', 'error');
        });
    }

    // 动态添加论文卡片
    function addPaperCard(paper) {
        // 如果是第一篇论文，隐藏空状态
        const emptyState = document.querySelector('.empty-state');
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // 创建卡片元素
        const cardElement = document.createElement('div');
        cardElement.className = 'paper-card';
        cardElement.dataset.paperId = paper.id;
        
        // 截断摘要
        const abstractText = paper.abstract.length > 200 ? 
            paper.abstract.substring(0, 200) + '...' : 
            paper.abstract;

        // PDF状态徽章
        const pdfBadge = paper.has_pdf ? 
            '<span class="pdf-badge pdf-available">📄 PDF</span>' : 
            '<span class="pdf-badge pdf-unavailable">❌ 无PDF</span>';

        // 预阅读按钮（仅在有PDF时显示）
        const preReadButton = paper.has_pdf ? 
            `<button class="pre-read-button" data-paper-id="${paper.id}" onclick="goToPreRead(${paper.id})">
                <span class="pre-read-icon">📖</span>
                预阅读
            </button>` : '';

        cardElement.innerHTML = `
            <div class="card-header">
                <div class="paper-source">${paper.source}</div>
                <div class="card-header-right">
                    <div class="top-row">
                        ${pdfBadge}
                        <div class="card-menu">
                            <button class="menu-trigger" onclick="toggleMenu(${paper.id})">
                                <span class="dots">⋯</span>
                            </button>
                            <div class="menu-dropdown" id="menu-${paper.id}">
                                <button class="menu-item delete-item" onclick="deletePaper(${paper.id}, '${paper.title.substring(0, 30).replace(/'/g, "\\'")}')">
                                    <span class="menu-icon">🗑️</span>
                                    删除论文
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <h3 class="paper-title">${paper.title}</h3>
                <p class="paper-authors">${paper.authors}</p>
                <p class="paper-abstract">${abstractText}</p>
                <div class="paper-date-bottom">${paper.date}</div>
            </div>
            <div class="card-footer">
                <div class="card-actions">
                    <button class="chat-button" data-paper-id="${paper.id}" onclick="goToChat(${paper.id})">
                        <span class="chat-icon">💬</span>
                        开始对话
                    </button>
                    ${preReadButton}
                </div>
            </div>
        `;

        // 添加到网格
        papersGrid.appendChild(cardElement);

        // 添加动画效果
        cardElement.style.opacity = '0';
        cardElement.style.transform = 'translateY(20px)';
        setTimeout(() => {
            cardElement.style.opacity = '1';
            cardElement.style.transform = 'translateY(0)';
        }, 100);
    }

    // 显示加载状态
    function showLoading(message = '正在处理...') {
        if (loadingOverlay) {
            document.querySelector('.loading-text').textContent = message;
            loadingOverlay.style.display = 'flex';
        }
    }

    // 隐藏加载状态
    function hideLoading() {
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
                <span class="notification-message">${message}</span>
            </div>
        `;

        // 添加样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4ade80' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1001;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
});

// 全局函数：跳转到聊天页面
function goToChat(paperId) {
    window.location.href = `/chat/${paperId}`;
}

// 全局函数：跳转到预阅读页面
function goToPreRead(paperId) {
    window.location.href = `/view_pre_read/${paperId}`;
}

// 全局函数：切换菜单显示
function toggleMenu(paperId) {
    const menu = document.getElementById('menu-' + paperId);
    const allMenus = document.querySelectorAll('.menu-dropdown');
    
    // 关闭其他菜单
    allMenus.forEach(m => {
        if (m !== menu) {
            m.classList.remove('show');
        }
    });
    
    // 切换当前菜单
    menu.classList.toggle('show');
}

// 删除论文功能
function deletePaper(paperId, paperTitle) {
    console.log('deletePaper called with:', paperId, paperTitle);
    // 显示确认对话框
    showConfirmDialog(
        '确认删除论文',
        `确定要删除论文"${paperTitle}"吗？此操作将同时删除PDF文件，且无法撤销。`,
        () => {
            // 用户确认删除
            performDelete(paperId);
        }
    );
}

// 执行删除操作
function performDelete(paperId) {
    console.log('performDelete called with:', paperId);
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        document.querySelector('.loading-text').textContent = '正在删除论文...';
        loadingOverlay.style.display = 'flex';
    }
    
    fetch(`/delete_paper/${paperId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
        
        if (data.success) {
            showNotification(`论文"${data.deleted_paper.title}"删除成功！`, 'success');
            
            // 移除卡片元素
            const cardElement = document.querySelector(`[data-paper-id="${paperId}"]`);
            if (cardElement) {
                cardElement.style.opacity = '0';
                cardElement.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    cardElement.remove();
                    
                    // 如果没有论文了，显示空状态
                    const remainingCards = document.querySelectorAll('.paper-card');
                    if (remainingCards.length === 0) {
                        location.reload(); // 重新加载页面显示空状态
                    }
                }, 300);
            }
        } else {
            showNotification(data.error || '删除论文失败', 'error');
        }
    })
    .catch(error => {
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
        console.error('Delete error:', error);
        showNotification('删除论文时发生网络错误', 'error');
    });
}

// 显示确认对话框
function showConfirmDialog(title, message, onConfirm) {
    console.log('showConfirmDialog called');
    const dialog = document.createElement('div');
    dialog.className = 'confirm-dialog';
    dialog.innerHTML = `
        <div class="confirm-content">
            <div class="confirm-title">${title}</div>
            <div class="confirm-message">${message}</div>
            <div class="confirm-actions">
                <button class="confirm-btn cancel" onclick="hideConfirmDialog()">取消</button>
                <button class="confirm-btn danger" onclick="confirmAction()">确认删除</button>
            </div>
        </div>
    `;
    
    // 保存确认回调
    dialog.confirmCallback = onConfirm;
    
    // 添加到页面
    document.body.appendChild(dialog);
    
    // 设置全局引用以便其他函数访问
    window.currentConfirmDialog = dialog;
    
    // 点击背景关闭
    dialog.addEventListener('click', function(e) {
        if (e.target === dialog) {
            hideConfirmDialog();
        }
    });
}

// 隐藏确认对话框
function hideConfirmDialog() {
    if (window.currentConfirmDialog) {
        document.body.removeChild(window.currentConfirmDialog);
        window.currentConfirmDialog = null;
    }
}

// 确认操作
function confirmAction() {
    if (window.currentConfirmDialog && window.currentConfirmDialog.confirmCallback) {
        window.currentConfirmDialog.confirmCallback();
    }
    hideConfirmDialog();
}

// 显示通知（全局函数）
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
            <span class="notification-message">${message}</span>
        </div>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4ade80' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;

    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 海报生成功能
function generatePoster(paperId) {
    const button = document.querySelector(`[data-paper-id="${paperId}"].poster-button`);
    if (!button) return;

    // 禁用按钮并显示加载状态
    button.disabled = true;
    const originalText = button.innerHTML;
    button.innerHTML = '<span class="poster-icon">⏳</span>生成中...';

    fetch(`/api/generate_poster/${paperId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('海报生成已开始，请稍候...', 'success');
            // 开始轮询状态
            pollPosterStatus(paperId, button, originalText);
        } else {
            showNotification(data.error || '海报生成启动失败', 'error');
            button.disabled = false;
            button.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('海报生成错误:', error);
        showNotification('海报生成启动失败', 'error');
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// 轮询海报生成状态
function pollPosterStatus(paperId, button, originalText) {
    const pollInterval = setInterval(() => {
        fetch(`/api/poster_status/${paperId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.status === 'completed') {
                    clearInterval(pollInterval);
                    button.disabled = false;
                    button.innerHTML = '<span class="poster-icon">✅</span>查看海报';
                    button.onclick = () => viewPosterFiles(paperId);
                    showNotification('海报生成完成！', 'success');
                } else if (data.status === 'error') {
                    clearInterval(pollInterval);
                    button.disabled = false;
                    button.innerHTML = originalText;
                    showNotification(`海报生成失败: ${data.error || '未知错误'}`, 'error');
                } else if (data.status === 'running') {
                    button.innerHTML = `<span class="poster-icon">⏳</span>${data.progress || '生成中...'}`;
                }
            }
        })
        .catch(error => {
            console.error('状态查询错误:', error);
        });
    }, 3000); // 每3秒查询一次状态

    // 30分钟后停止轮询
    setTimeout(() => {
        clearInterval(pollInterval);
        if (button.disabled) {
            button.disabled = false;
            button.innerHTML = originalText;
            showNotification('海报生成超时，请重试', 'error');
        }
    }, 30 * 60 * 1000);
}

// 查看海报文件
function viewPosterFiles(paperId) {
    fetch(`/api/poster_files/${paperId}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.files.length > 0) {
            showPosterFilesModal(paperId, data.files);
        } else {
            showNotification('没有找到海报文件', 'error');
        }
    })
    .catch(error => {
        console.error('获取海报文件错误:', error);
        showNotification('获取海报文件失败', 'error');
    });
}

// 显示海报文件模态框
function showPosterFilesModal(paperId, files) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'poster-modal';
    modal.innerHTML = `
        <div class="poster-modal-content">
            <div class="poster-modal-header">
                <h3>生成的海报文件</h3>
                <button class="poster-modal-close">&times;</button>
            </div>
            <div class="poster-modal-body">
                <div class="poster-files-list">
                    ${files.map(file => `
                        <div class="poster-file-item">
                            <div class="file-info">
                                <span class="file-icon">${getFileIcon(file.type)}</span>
                                <span class="file-name">${file.name}</span>
                                <span class="file-size">${formatFileSize(file.size)}</span>
                            </div>
                            <button class="download-btn" onclick="downloadPosterFile(${paperId}, '${file.path}')">
                                下载
                            </button>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(modal);

    // 绑定关闭事件
    const closeBtn = modal.querySelector('.poster-modal-close');
    closeBtn.onclick = () => {
        document.body.removeChild(modal);
    };

    // 点击外部关闭
    modal.onclick = (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    };
}

// 下载海报文件
function downloadPosterFile(paperId, filePath) {
    const url = `/api/download_poster/${paperId}/${encodeURIComponent(filePath)}`;
    const a = document.createElement('a');
    a.href = url;
    a.download = '';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// 获取文件图标
function getFileIcon(fileType) {
    switch (fileType) {
        case '.png':
        case '.jpg':
        case '.jpeg':
            return '🖼️';
        case '.pptx':
            return '📊';
        case '.pdf':
            return '📄';
        default:
            return '📁';
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}